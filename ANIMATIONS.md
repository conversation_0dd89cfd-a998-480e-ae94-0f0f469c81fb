# Realome Theme GSAP Liquid Button Animations

This document explains the GSAP liquid button animations added to the Realome theme.

## What's Included

### **Liquid Button Effects**
These animations work automatically on all buttons without any additional code:

- **Liquid Hover Effects**: Animated liquid background with wave effects and color gradients
- **Button Scale Effects**: Buttons scale up (1.05x) and get enhanced shadows on hover
- **Button Click Effects**: Buttons briefly scale down (0.95x) when clicked for tactile feedback
- **Mouse-Responsive Gradients**: Liquid colors follow mouse movement for interactive feel
- **Smooth Transitions**: All animations use smooth easing for professional feel

### **Liquid Button Animation Details**
All WordPress and theme buttons automatically get these effects:

**Liquid Effects:**
- Animated canvas overlay with wave effects
- Mouse-responsive color gradients that follow cursor
- Smooth color transitions with HSL color cycling
- Wavy borders that pulse and move
- Only activates on buttons larger than 80x30px

**Scale Effects:**
- Scale up to 1.05x (5% larger) on hover
- Enhanced shadow: `0 8px 25px rgba(0, 0, 0, 0.15)`
- Smooth 0.3s transition with power2.out easing

**Click Effects:**
- Brief scale down to 0.95x when mouse pressed
- Returns to hover state (1.05x) when mouse released
- Quick 0.1s transitions for responsive feel

**Affected Elements:**
- `.wp-block-button__link` (WordPress block buttons)
- `.wp-element-button` (WordPress element buttons)
- `button` (HTML buttons)
- `input[type="submit"]` (Form submit buttons)
- `input[type="button"]` (Form buttons)
- `.btn` (Custom button class)
- `a.button` (Link buttons)

## Technical Details

### **CSS Enhancements**
The theme includes CSS that enhances the button animations:

- **Smooth Transitions**: All buttons have `transition: all 0.3s ease`
- **Transform Origin**: Set to `center` for balanced scaling
- **Performance**: Uses `will-change: transform, box-shadow` for optimization
- **Gradient Overlay**: Subtle gradient appears on hover for extra polish
- **Border Radius**: Enhanced 8px radius for modern look

### **Accessibility**
- **Focus States**: Enhanced focus outlines for keyboard navigation
- **Reduced Motion**: Respects `prefers-reduced-motion: reduce` setting
- **Screen Reader Friendly**: Animations don't interfere with assistive technology

## Customization

### **Disable Liquid Effects**
To disable liquid effects while keeping scale animations, edit the JavaScript file:
```javascript
// In animations.js, change this line:
const LIQUID_EFFECT_ENABLED = false; // Set to false to disable liquid effects
```

### **Disable All Button Animations**
To disable all button animations, add this CSS:
```css
.wp-block-button__link,
.wp-element-button,
button,
input[type="submit"],
input[type="button"] {
    transition: none !important;
}
```

### **Modify Animation Speed**
Edit the JavaScript file to change timing:
```javascript
// In animations.js, change duration values
duration: 0.3, // Make faster: 0.2, slower: 0.5
```

### **Customize Scale Amount**
Change the scale values in animations.js:
```javascript
scale: 1.05, // Make more dramatic: 1.1, more subtle: 1.02
```

### **Modify Shadow Effects**
Adjust the boxShadow values:
```javascript
boxShadow: "0 8px 25px rgba(0, 0, 0, 0.15)", // Customize as needed
```

## Browser Support

- **Modern Browsers**: Full GSAP 3.12.2 support
- **Fallback**: CSS transitions for older browsers
- **Mobile**: Optimized for touch devices
- **Performance**: Hardware accelerated animations

## Files Modified

1. **`realome/functions.php`** - Added GSAP script and button styles
2. **`realome/assets/js/animations.js`** - Button animation logic
3. **`realome/ANIMATIONS.md`** - This documentation

## Troubleshooting

### **Animations Not Working:**
1. Check browser console for JavaScript errors
2. Ensure GSAP is loading (check Network tab in DevTools)
3. Verify buttons exist when script runs

### **Performance Issues:**
1. Reduce animation duration (use 0.2s instead of 0.3s)
2. Disable animations on mobile if needed
3. Check for conflicting CSS transitions

### **Plugin Conflicts:**
1. Ensure GSAP loads before other animation libraries
2. Check for jQuery conflicts in console
3. Test with other plugins disabled

## What's Next?

The button animations are now active! You can:
1. **Test them** by hovering over any button on your site
2. **Customize** the effects using the code examples above
3. **Add more animations** later if needed (just ask!)

The animations will work on all WordPress buttons, form buttons, and any custom buttons using the classes listed above.
