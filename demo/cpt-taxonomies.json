{"locations": {"name": "locations", "label": "Locations", "singular_label": "Location", "description": "", "public": "true", "publicly_queryable": "true", "hierarchical": "true", "show_ui": "true", "show_in_menu": "true", "show_in_nav_menus": "true", "query_var": "true", "query_var_slug": "", "rewrite": "true", "rewrite_slug": "", "rewrite_withfront": "1", "rewrite_hierarchical": "0", "show_admin_column": "true", "show_in_rest": "true", "show_tagcloud": "false", "sort": "false", "show_in_quick_edit": "true", "rest_base": "", "rest_controller_class": "", "labels": {"menu_name": "", "all_items": "", "edit_item": "", "view_item": "", "update_item": "", "add_new_item": "", "new_item_name": "", "parent_item": "", "parent_item_colon": "", "search_items": "", "popular_items": "", "separate_items_with_commas": "", "add_or_remove_items": "", "choose_from_most_used": "", "not_found": "", "no_terms": "", "items_list_navigation": "", "items_list": "", "back_to_items": "", "name_field_description": "", "parent_field_description": "", "slug_field_description": "", "desc_field_description": ""}, "meta_box_cb": "", "default_term": "", "object_types": ["listing"]}, "property_types": {"name": "property_types", "label": "Property Types", "singular_label": "Property Type", "description": "", "public": "true", "publicly_queryable": "true", "hierarchical": "true", "show_ui": "true", "show_in_menu": "true", "show_in_nav_menus": "true", "query_var": "true", "query_var_slug": "", "rewrite": "true", "rewrite_slug": "", "rewrite_withfront": "1", "rewrite_hierarchical": "0", "show_admin_column": "false", "show_in_rest": "true", "show_tagcloud": "false", "show_in_quick_edit": "true", "rest_base": "", "rest_controller_class": "", "labels": {"menu_name": "", "all_items": "", "edit_item": "", "view_item": "", "update_item": "", "add_new_item": "", "new_item_name": "", "parent_item": "", "parent_item_colon": "", "search_items": "", "popular_items": "", "separate_items_with_commas": "", "add_or_remove_items": "", "choose_from_most_used": "", "not_found": "", "no_terms": "", "items_list_navigation": "", "items_list": "", "back_to_items": ""}, "meta_box_cb": "", "default_term": "", "object_types": ["listing"]}, "amenities": {"name": "amenities", "label": "Amenities", "singular_label": "Amenity", "description": "", "public": "true", "publicly_queryable": "true", "hierarchical": "true", "show_ui": "true", "show_in_menu": "true", "show_in_nav_menus": "true", "query_var": "true", "query_var_slug": "", "rewrite": "true", "rewrite_slug": "", "rewrite_withfront": "1", "rewrite_hierarchical": "0", "show_admin_column": "false", "show_in_rest": "true", "show_tagcloud": "false", "show_in_quick_edit": "true", "rest_base": "", "rest_controller_class": "", "labels": {"menu_name": "", "all_items": "", "edit_item": "", "view_item": "", "update_item": "", "add_new_item": "", "new_item_name": "", "parent_item": "", "parent_item_colon": "", "search_items": "", "popular_items": "", "separate_items_with_commas": "", "add_or_remove_items": "", "choose_from_most_used": "", "not_found": "", "no_terms": "", "items_list_navigation": "", "items_list": "", "back_to_items": ""}, "meta_box_cb": "", "default_term": "", "object_types": ["listing"]}, "testimonials_category": {"name": "testimonials_category", "label": "Categories", "singular_label": "Category", "description": "", "public": "true", "publicly_queryable": "true", "hierarchical": "false", "show_ui": "true", "show_in_menu": "true", "show_in_nav_menus": "true", "query_var": "true", "query_var_slug": "", "rewrite": "true", "rewrite_slug": "", "rewrite_withfront": "1", "rewrite_hierarchical": "0", "show_admin_column": "false", "show_in_rest": "true", "show_tagcloud": "false", "show_in_quick_edit": "", "rest_base": "", "rest_controller_class": "", "labels": {"menu_name": "", "all_items": "", "edit_item": "", "view_item": "", "update_item": "", "add_new_item": "", "new_item_name": "", "parent_item": "", "parent_item_colon": "", "search_items": "", "popular_items": "", "separate_items_with_commas": "", "add_or_remove_items": "", "choose_from_most_used": "", "not_found": "", "no_terms": "", "items_list_navigation": "", "items_list": "", "back_to_items": ""}, "meta_box_cb": "", "default_term": "", "object_types": ["testimonials"]}, "portfolio_category": {"name": "portfolio_category", "label": "Portfolio Categories", "singular_label": "Category", "description": "", "public": "true", "publicly_queryable": "true", "hierarchical": "true", "show_ui": "true", "show_in_menu": "true", "show_in_nav_menus": "true", "query_var": "true", "query_var_slug": "", "rewrite": "true", "rewrite_slug": "", "rewrite_withfront": "1", "rewrite_hierarchical": "0", "show_admin_column": "true", "show_in_rest": "true", "show_tagcloud": "false", "show_in_quick_edit": "", "rest_base": "", "rest_controller_class": "", "labels": {"menu_name": "", "all_items": "", "edit_item": "", "view_item": "", "update_item": "", "add_new_item": "", "new_item_name": "", "parent_item": "", "parent_item_colon": "", "search_items": "", "popular_items": "", "separate_items_with_commas": "", "add_or_remove_items": "", "choose_from_most_used": "", "not_found": "", "no_terms": "", "items_list_navigation": "", "items_list": "", "back_to_items": ""}, "meta_box_cb": "", "default_term": "", "object_types": ["portfolio"]}, "listing_type": {"name": "listing_type", "label": "Listing Types", "singular_label": "Listing Type", "description": "", "public": "true", "publicly_queryable": "true", "hierarchical": "false", "show_ui": "true", "show_in_menu": "true", "show_in_nav_menus": "true", "query_var": "true", "query_var_slug": "", "rewrite": "true", "rewrite_slug": "", "rewrite_withfront": "1", "rewrite_hierarchical": "0", "show_admin_column": "false", "show_in_rest": "true", "show_tagcloud": "false", "sort": "false", "show_in_quick_edit": "", "rest_base": "", "rest_controller_class": "", "labels": {"menu_name": "", "all_items": "", "edit_item": "", "view_item": "", "update_item": "", "add_new_item": "", "new_item_name": "", "parent_item": "", "parent_item_colon": "", "search_items": "", "popular_items": "", "separate_items_with_commas": "", "add_or_remove_items": "", "choose_from_most_used": "", "not_found": "", "no_terms": "", "items_list_navigation": "", "items_list": "", "back_to_items": "", "name_field_description": "", "parent_field_description": "", "slug_field_description": "", "desc_field_description": ""}, "meta_box_cb": "", "default_term": "", "object_types": ["listing"]}}